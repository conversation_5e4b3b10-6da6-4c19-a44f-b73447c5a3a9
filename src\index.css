@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--white);
  --color-foreground: var(--navy-dark);
  --color-card: var(--white);
  --color-card-foreground: var(--navy-dark);
  --color-popover: var(--white);
  --color-popover-foreground: var(--navy-dark);
  --color-primary: var(--blue-medium);
  --color-primary-foreground: var(--white);
  --color-secondary: var(--blue-light-gray);
  --color-secondary-foreground: var(--navy-dark);
  --color-muted: var(--blue-light-gray);
  --color-muted-foreground: var(--gray-medium);
  --color-accent: var(--teal-bright);
  --color-accent-foreground: var(--white);
  --color-destructive: var(--red-error);
  --color-success: var(--green-success);
  --color-warning: var(--orange-highlight);
  --color-border: var(--blue-light-gray);
  --color-input: var(--blue-light-gray);
  --color-ring: var(--teal-bright);
  --color-chart-1: var(--blue-medium);
  --color-chart-2: var(--teal-bright);
  --color-chart-3: var(--navy-dark);
  --color-chart-4: var(--orange-highlight);
  --color-chart-5: var(--blue-light-gray);
  --color-sidebar: var(--gray-lightest);
  --color-sidebar-foreground: var(--navy-dark);
  --color-sidebar-primary: var(--blue-medium);
  --color-sidebar-primary-foreground: var(--white);
  --color-sidebar-accent: var(--teal-bright);
  --color-sidebar-accent-foreground: var(--white);
  --color-sidebar-border: var(--blue-light-gray);
  --color-sidebar-ring: var(--teal-bright);
}

:root {
  --radius: 0.625rem;

  /* Base Palette Colors */
  --navy-dark: #112F5D;
  --blue-medium: #205EBC;
  --teal-bright: #00AFB9;
  --blue-light-gray: #C5D5EC;
  --orange-highlight: #D97706;

  /* Semantic Colors */
  --red-error: #B91C1C;
  --red-error-light: #ef4444;
  --green-success: #0F7B0F;
  --green-success-light: #22c55e;

  /* Neutral Colors */
  --white: #FFFFFF;
  --gray-lightest: #F8FAFC;
  --gray-medium: #6B7280;
  --gray-dark: #374151;

  /* Theme Variables - Light Mode */
  --background: var(--white);
  --foreground: var(--navy-dark);
  --card: var(--white);
  --card-foreground: var(--navy-dark);
  --popover: var(--white);
  --popover-foreground: var(--navy-dark);
  --primary: var(--blue-medium);
  --primary-foreground: var(--white);
  --secondary: var(--blue-light-gray);
  --secondary-foreground: var(--navy-dark);
  --muted: var(--blue-light-gray);
  --muted-foreground: var(--gray-medium);
  --accent: var(--teal-bright);
  --accent-foreground: var(--white);
  --destructive: var(--red-error);
  --success: var(--green-success);
  --warning: var(--orange-highlight);
  --border: var(--blue-light-gray);
  --input: var(--blue-light-gray);
  --ring: var(--teal-bright);
  --chart-1: var(--blue-medium);
  --chart-2: var(--teal-bright);
  --chart-3: var(--navy-dark);
  --chart-4: var(--orange-highlight);
  --chart-5: var(--blue-light-gray);
  --sidebar: var(--gray-lightest);
  --sidebar-foreground: var(--navy-dark);
  --sidebar-primary: var(--blue-medium);
  --sidebar-primary-foreground: var(--white);
  --sidebar-accent: var(--teal-bright);
  --sidebar-accent-foreground: var(--white);
  --sidebar-border: var(--blue-light-gray);
  --sidebar-ring: var(--teal-bright);
}

.dark {
  /* Dark Theme Variations */
  --navy-lighter: #1a3a6b;
  --blue-medium-dark: #1e56a8;
  --teal-muted: #008a94;
  --gray-dark-surface: #1f2937;
  --border-dark: rgba(255, 255, 255, 0.1);
  --input-dark: rgba(255, 255, 255, 0.15);

  /* Theme Variables - Dark Mode */
  --background: var(--navy-dark);
  --foreground: var(--white);
  --card: var(--navy-lighter);
  --card-foreground: var(--white);
  --popover: var(--navy-lighter);
  --popover-foreground: var(--white);
  --primary: var(--blue-light-gray);
  --primary-foreground: var(--navy-dark);
  --secondary: var(--gray-dark-surface);
  --secondary-foreground: var(--white);
  --muted: var(--gray-dark-surface);
  --muted-foreground: var(--gray-medium);
  --accent: var(--teal-bright);
  --accent-foreground: var(--navy-dark);
  --destructive: var(--red-error-light);
  --success: var(--green-success-light);
  --warning: var(--orange-highlight);
  --border: var(--border-dark);
  --input: var(--input-dark);
  --ring: var(--teal-muted);
  --chart-1: var(--blue-medium);
  --chart-2: var(--teal-bright);
  --chart-3: var(--orange-highlight);
  --chart-4: var(--blue-light-gray);
  --chart-5: var(--red-error-light);
  --sidebar: var(--navy-lighter);
  --sidebar-foreground: var(--white);
  --sidebar-primary: var(--blue-medium);
  --sidebar-primary-foreground: var(--white);
  --sidebar-accent: var(--teal-bright);
  --sidebar-accent-foreground: var(--navy-dark);
  --sidebar-border: var(--border-dark);
  --sidebar-ring: var(--teal-muted);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
