import { <PERSON><PERSON> } from "@/components/ui/button"
import { ThemeProvider } from "@/components/theme-provider"
import { ModeToggle } from "@/components/mode-toggle"
import planfulyLogo from '/logo/Planfuly_Logo.png'

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="planfuly-ui-theme">
      <div className="relative">
        <div className="absolute top-4 right-4">
          <ModeToggle />
        </div>

        <div>
          <img src={planfulyLogo} className="logo" alt="Planfuly logo" />
        </div>
        <h1>Engage Planfuly</h1>

        <div className="flex min-h-svh flex-col items-center justify-center">
          <Button>Click me</Button>
        </div>

        <p className="footer">
          Planfuly Inc.
        </p>
      </div>
    </ThemeProvider>
  )
}

export default App
